// Basic UI interactions: nav menu, reveal on scroll, tilt, year, and cart drawer toggle
(function () {
  const $ = (s, el=document) => el.querySelector(s);
  const $$ = (s, el=document) => Array.from(el.querySelectorAll(s));

  // Current year in footer
  const year = new Date().getFullYear();
  const yearEl = $('#year');
  if (yearEl) yearEl.textContent = String(year);

  // Mobile nav toggle
  const toggle = $('.nav-toggle');
  const menu = $('#menu');
  if (toggle && menu) {
    toggle.addEventListener('click', () => {
      const open = menu.classList.toggle('open');
      toggle.setAttribute('aria-expanded', String(open));
    });
  }

  // Reveal on scroll
  const io = new IntersectionObserver((entries) => {
    entries.forEach((e) => {
      if (e.isIntersecting) {
        e.target.classList.add('visible');
        io.unobserve(e.target);
      }
    });
  }, { threshold: 0.15 });
  $$('.reveal').forEach(el => io.observe(el));

  // Simple tilt effect
  $$('.tilt').forEach(card => {
    let rAF = 0;
    const onMove = (e) => {
      const rect = card.getBoundingClientRect();
      const x = (e.clientX - rect.left) / rect.width - 0.5;
      const y = (e.clientY - rect.top) / rect.height - 0.5;
      cancelAnimationFrame(rAF);
      rAF = requestAnimationFrame(() => {
        card.style.transform = `rotateY(${x * 6}deg) rotateX(${ -y * 6}deg)`;
      });
    };
    const reset = () => card.style.transform = '';
    card.addEventListener('mousemove', onMove);
    card.addEventListener('mouseleave', reset);
  });

  // Cart drawer toggle (placeholder; actual cart managed in shopify.js)
  const drawer = document.querySelector('[data-cart-drawer]');
  const toggles = $$('[data-cart-toggle]');
  const toggleDrawer = () => drawer && drawer.classList.toggle('open');
  toggles.forEach(t => t.addEventListener('click', (e) => { e.preventDefault(); toggleDrawer(); }));
})();

