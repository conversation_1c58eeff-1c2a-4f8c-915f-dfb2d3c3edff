/* Light theme overrides for shop page */
.shop-main {
  background: #ffffff;
  color: #1a1a1a;
  min-height: 100vh;
  position: relative;
}

.shop-main::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(0,225,255,0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,225,255,0.03) 1px, transparent 1px);
  background-size: 30px 30px;
  pointer-events: none;
  z-index: 0;
}

.shop-main .container {
  position: relative;
  z-index: 1;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.shop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.shop-header .section-title {
  color: #1a1a1a;
  margin: 0;
}

.shop-filters {
  display: flex;
  gap: 1rem;
}

.filter-select {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  color: #1a1a1a;
  font-size: 0.9rem;
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.filter-select:hover {
  border-color: #00e1ff;
}

.filter-select:focus {
  outline: none;
  border-color: #00e1ff;
  box-shadow: 0 0 0 3px rgba(0, 225, 255, 0.1);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.product-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.3s ease, border-color 0.2s ease;
  position: relative;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 225, 255, 0.15), 0 4px 20px rgba(122, 92, 255, 0.1);
  border-color: rgba(0, 225, 255, 0.3);
}

.product-image {
  height: 180px;
  position: relative;
  overflow: hidden;
}

.placeholder-img {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "Space Grotesk", sans-serif;
  font-weight: 700;
  font-size: 1.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.materials-bg { background: linear-gradient(135deg, #00e1ff, #0099cc); }
.petg-bg { background: linear-gradient(135deg, #7a5cff, #5b3dff); }
.abs-bg { background: linear-gradient(135deg, #22d3a6, #1ba085); }
.resin-bg { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
.service-bg { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
.rush-bg { background: linear-gradient(135deg, #feca57, #ff9ff3); }
.consult-bg { background: linear-gradient(135deg, #a8e6cf, #7fcdcd); }
.dfm-bg { background: linear-gradient(135deg, #ffd93d, #6bcf7f); }

.product-info {
  padding: 1.25rem;
}

.product-info h3 {
  margin: 0 0 0.5rem 0;
  color: #1a1a1a;
  font-family: "Space Grotesk", sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
}

.product-description {
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0 0 1rem 0;
}

.product-price {
  font-family: "Space Grotesk", sans-serif;
  font-weight: 700;
  font-size: 1.25rem;
  color: #00e1ff;
  margin-bottom: 1rem;
}

.product-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.quantity-selector {
  display: flex;
  align-items: center;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  overflow: hidden;
}

.qty-btn {
  background: #f8f9fa;
  border: none;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6c757d;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.qty-btn:hover {
  background: #00e1ff;
  color: white;
}

.qty-input {
  border: none;
  width: 50px;
  height: 32px;
  text-align: center;
  background: white;
  color: #1a1a1a;
  font-weight: 500;
}

.qty-input:focus {
  outline: none;
}

.add-to-cart {
  flex: 1;
  justify-content: center;
  min-height: 36px;
  font-size: 0.9rem;
  background: linear-gradient(135deg, rgba(0,225,255,0.9), rgba(122,92,255,0.9));
  border: 1px solid rgba(0,225,255,0.3);
  color: white;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.add-to-cart:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 225, 255, 0.3);
  background: linear-gradient(135deg, #00e1ff, #7a5cff);
}

.add-to-cart:active {
  transform: translateY(0);
}

/* Cart notification */
.cart-notification {
  position: fixed;
  top: 80px;
  right: 20px;
  background: linear-gradient(135deg, #22d3a6, #1ba085);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 20px rgba(34, 211, 166, 0.3);
  transform: translateX(400px);
  transition: transform 0.3s ease;
  z-index: 1000;
}

.cart-notification.show {
  transform: translateX(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .shop-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .shop-filters {
    justify-content: center;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .product-controls {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .quantity-selector {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .shop-filters {
    flex-direction: column;
  }
}

/* Loading states */
.product-card.loading {
  opacity: 0.6;
  pointer-events: none;
}

.product-card.loading .add-to-cart {
  background: #e9ecef;
  color: #6c757d;
}

/* Filter animations */
.product-card.filtered-out {
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

.products-grid {
  transition: all 0.3s ease;
}
