// Minimal Shopify Storefront API integration scaffold
// Replace the placeholders with your actual domain and Storefront access token
// This file provides: cart state, add-to-cart, and checkout redirect via Shopify Cart permalinks

const ShopifyConfig = {
  storeDomain: 'sparklesphere.store',
  storefrontToken: 'STOREFRONT_ACCESS_TOKEN',
  apiVersion: '2024-07',
};

const Cart = (() => {
  let items = []; // {variantId, title, qty, price}

  const render = () => {
    const list = document.getElementById('cart-items');
    const totalEl = document.getElementById('cart-total');
    if (!list || !totalEl) return;
    list.innerHTML = '';
    let total = 0;
    items.forEach((it, idx) => {
      total += it.price * it.qty;
      const row = document.createElement('div');
      row.className = 'card';
      row.innerHTML = `
        <div style="display:flex; align-items:center; justify-content:space-between; gap:.6rem;">
          <div><strong>${it.title}</strong><div class="muted">x${it.qty}</div></div>
          <div>$${(it.price * it.qty).toFixed(2)}</div>
        </div>`;
      list.appendChild(row);
    });
    totalEl.textContent = `$${total.toFixed(2)}`;
    const countEl = document.querySelector('.cart-count');
    if (countEl) countEl.textContent = String(items.reduce((a,b) => a + b.qty, 0));
  };

  const add = (variantId, title, price, qty = 1) => {
    const existing = items.find(i => i.variantId === variantId);
    if (existing) existing.qty += qty; else items.push({ variantId, title, price, qty });
    render();
  };

  const checkout = () => {
    if (!items.length) return;
    const lines = items.map(i => `${i.variantId}:${i.qty}`).join(',');
    // Shopify cart permalink: /cart/{variant_id}:{quantity},{variant_id}:{quantity}
    window.location.href = `https://${ShopifyConfig.storeDomain}/cart/${lines}`;
  };

  return { add, render, checkout };
})();

// Bind checkout button
(function(){
  const btn = document.getElementById('checkout-btn');
  if (btn) btn.addEventListener('click', () => Cart.checkout());
})();

// Example: bind buttons with data attributes
// <button data-add-to-cart data-variant="VARIANT_ID" data-title="NAME" data-price="12.34">Add</button>
document.addEventListener('click', (e) => {
  const t = e.target;
  if (!(t instanceof HTMLElement)) return;
  const el = t.closest('[data-add-to-cart]');
  if (!el) return;
  e.preventDefault();
  const variant = el.getAttribute('data-variant');
  const title = el.getAttribute('data-title') || 'Custom Print';
  const price = parseFloat(el.getAttribute('data-price') || '0');
  const qty = parseInt(el.getAttribute('data-qty') || '1', 10);
  if (variant) Cart.add(variant, title, price, qty);
});

