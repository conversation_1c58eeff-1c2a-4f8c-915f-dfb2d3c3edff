<!doctype html>
<html lang="en" data-theme="dark">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Get a Quote – Contact | Your 3D Printing Business</title>
  <link rel="stylesheet" href="/css/styles.css" />
</head>
<body>
  <header class="site-header">
    <div class="container nav">
      <a href="/" class="brand"><span class="logo-dot"></span><span class="brand-text">Your 3D Printing Business</span></a>
      <nav aria-label="Primary">
        <button class="nav-toggle" aria-expanded="false" aria-controls="menu">☰</button>
        <ul id="menu" class="menu">
          <li><a href="/services.html">Services</a></li>
          <li><a href="/portfolio.html">Portfolio</a></li>
          <li><a href="/about.html">About</a></li>
          <li><a href="/blog/">Blog</a></li>
          <li><a href="/contact.html" class="btn btn-primary" aria-current="page">Get a Quote</a></li>
        </ul>
      </nav>
      <a href="#" class="cart-link" aria-label="Cart" data-cart-toggle>
        <span class="icon-cart" aria-hidden="true"></span>
        <span class="cart-count" aria-live="polite">0</span>
      </a>
    </div>
  </header>

  <main id="main" class="container">
    <h1 class="section-title">Request a Quote</h1>
    <form class="card" action="#" method="post" enctype="multipart/form-data" onsubmit="event.preventDefault(); alert('Submitted. Connect to form backend or Shopify contact form.');">
      <div class="grid two">
        <label>Full Name
          <input type="text" name="name" required placeholder="Jane Doe" />
        </label>
        <label>Email
          <input type="email" name="email" required placeholder="<EMAIL>" />
        </label>
      </div>
      <div class="grid two">
        <label>Material
          <select name="material" required>
            <option value="PLA+">PLA+</option>
            <option value="PETG">PETG</option>
            <option value="ABS/ASA">ABS/ASA</option>
            <option value="Resin">Resin</option>
          </select>
        </label>
        <label>Color
          <input type="text" name="color" placeholder="Black" />
        </label>
      </div>
      <div class="grid two">
        <label>Quantity
          <input type="number" min="1" name="qty" value="1" required />
        </label>
        <label>Finishing
          <select name="finish">
            <option value="Standard">Standard</option>
            <option value="Sanded">Sanded</option>
            <option value="Smoothed">Smoothed</option>
            <option value="Painted">Painted</option>
          </select>
        </label>
      </div>
      <label>Upload Model (STL/3MF)
        <input type="file" name="model" accept=".stl,.3mf,.obj" />
      </label>
      <label>Notes
        <textarea name="notes" rows="4" placeholder="Tolerances, critical dimensions, application, etc."></textarea>
      </label>
      <div style="display:flex; gap:.8rem; align-items:center;">
        <button class="btn btn-primary" type="submit">Send Request</button>
        <span class="muted">We'll respond within 24 hours.</span>
      </div>
    </form>
  </main>

  <footer class="site-footer">
    <div class="container footer-grid">
      <div>
        <div class="brand small"><span class="logo-dot"></span> Your 3D Printing Business</div>
        <p class="muted">High‑quality custom 3D printing for makers and engineers.</p>
      </div>
      <nav aria-label="Footer">
        <ul class="list">
          <li><a href="/services.html">Services</a></li>
          <li><a href="/portfolio.html">Portfolio</a></li>
          <li><a href="/about.html">About</a></li>
          <li><a href="/blog/">Blog</a></li>
          <li><a href="/contact.html">Contact</a></li>
        </ul>
      </nav>
      <div>
        <p class="muted">© <span id="year"></span> Your 3D Printing Business</p>
      </div>
    </div>
  </footer>

  <script src="/js/main.js" defer></script>
  <script src="/js/shopify.js" defer></script>
</body>
</html>

