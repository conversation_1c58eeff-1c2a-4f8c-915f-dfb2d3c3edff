/* Base: dark theme with neon accents */
:root {
  --bg: #0b0f14;
  --bg-elev: #0f151d;
  --text: #e6f0ff;
  --muted: #93a4bd;
  --primary: #00e1ff;     /* neon cyan */
  --primary-600: #00b8d6;
  --accent: #7a5cff;      /* neon purple */
  --accent-600: #5b3dff;
  --success: #22d3a6;     /* neon green */
  --surface: #0c131b;
  --card: #0f1822;
  --border: #1e2a3a;
  --shadow: 0 20px 40px rgba(0, 225, 255, 0.08), 0 6px 20px rgba(122, 92, 255, 0.06);
}

* { box-sizing: border-box; }
html, body { height: 100%; }
body {
  background: radial-gradient(1200px 800px at 80% -10%, rgba(122,92,255,0.12), transparent 60%),
              radial-gradient(1000px 800px at 0% 120%, rgba(0,225,255,0.10), transparent 60%),
              var(--bg);
  color: var(--text);
  margin: 0;
  font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img { max-width: 100%; display: block; }
a { color: var(--primary); text-decoration: none; }
a:hover { color: var(--accent); }

.container { width: min(1200px, 93vw); margin: 0 auto; padding: 0 1rem; }
.skip-link { position: absolute; left: -999px; top: -999px; }
.skip-link:focus { left: 1rem; top: 1rem; background: var(--accent); color: white; padding: .5rem 1rem; border-radius: .4rem; }

/* Header */
.site-header { position: sticky; top: 0; z-index: 40; background: rgba(11,15,20,0.7); backdrop-filter: saturate(1.2) blur(10px); border-bottom: 1px solid var(--border); }
.nav { display: flex; align-items: center; justify-content: space-between; padding: .8rem 0; gap: 1rem; }
.brand { display: inline-flex; align-items: center; gap: .6rem; font-weight: 600; color: var(--text); }
.brand .logo-dot { width: 12px; height: 12px; border-radius: 50%; background: linear-gradient(135deg, var(--primary), var(--accent)); box-shadow: 0 0 18px rgba(0,225,255,0.6), 0 0 26px rgba(122,92,255,0.5); }
.brand .brand-text { font-family: "Space Grotesk", Inter, system-ui, sans-serif; letter-spacing: .2px; }
.menu { display: flex; gap: 1rem; list-style: none; margin: 0; padding: 0; }
.menu a { color: var(--text); padding: .5rem .8rem; border-radius: .4rem; }
.menu a:hover { background: var(--bg-elev); }
.nav-toggle { display: none; }
.cart-link { position: relative; display: inline-flex; align-items: center; gap: .3rem; color: var(--text); }
.cart-count { position: absolute; top: -6px; right: -10px; background: var(--accent); color: white; border-radius: 999px; font-size: 12px; padding: 0 .35rem; }

/* Buttons */
.btn { display: inline-flex; align-items: center; justify-content: center; gap: .5rem; border: 1px solid var(--border); border-radius: .6rem; padding: .7rem 1rem; color: var(--text); background: linear-gradient(180deg, var(--card), var(--surface)); box-shadow: var(--shadow); transition: transform .15s ease, box-shadow .2s ease, border-color .2s ease; }
.btn:hover { transform: translateY(-1px); border-color: var(--accent); }
.btn-primary { background: linear-gradient(135deg, rgba(0,225,255,0.2), rgba(122,92,255,0.2)); border-color: rgba(0,225,255,0.4); }
.btn-ghost { background: transparent; border-color: var(--border); color: var(--text); }

/* Hero */
.hero { position: relative; overflow: clip; border-bottom: 1px solid var(--border); }
.hero-bg { position: absolute; inset: 0; background:
  radial-gradient(800px 400px at 70% 20%, rgba(0,225,255,0.18), transparent 60%),
  radial-gradient(600px 300px at 20% 80%, rgba(122,92,255,0.18), transparent 60%),
  url('../assets/wireframe-grid.svg');
  opacity: .9;
  mix-blend-mode: screen;
  pointer-events: none;
}
.hero-content { position: relative; padding: clamp(3rem, 6vw, 6rem) 0; }
.display { font-family: "Space Grotesk", Inter, system-ui, sans-serif; font-size: clamp(2rem, 4.5vw, 3.2rem); line-height: 1.1; letter-spacing: .5px; margin: 0 0 1rem; }
.lead { font-size: 1.1rem; color: var(--muted); max-width: 68ch; }
.cta-row { display: flex; gap: .8rem; margin-top: 1.2rem; }
.hero-stats { display: grid; grid-template-columns: repeat(3, minmax(0, 1fr)); gap: .8rem; margin-top: 2rem; }
.stat { background: var(--card); border: 1px solid var(--border); padding: .9rem; border-radius: .6rem; text-align: center; box-shadow: var(--shadow); }
.stat .num { font-family: "Space Grotesk", Inter, system-ui, sans-serif; font-weight: 700; color: var(--primary); font-size: 1.2rem; }
.stat .label { display: block; color: var(--muted); font-size: .9rem; }

/* Sections */
.section-title { font-family: "Space Grotesk"; margin: 2.5rem 0 1rem; font-size: 1.6rem; }
.grid { display: grid; gap: 1rem; }
.grid.two { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid.three { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid.four { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.card { background: linear-gradient(180deg, var(--card), var(--surface)); border: 1px solid var(--border); border-radius: .8rem; padding: 1rem; box-shadow: var(--shadow); }
.muted { color: var(--muted); }
.thumb { aspect-ratio: 4/3; background: radial-gradient(circle at 70% 30%, rgba(0,225,255,0.15), transparent 40%), radial-gradient(circle at 30% 70%, rgba(122,92,255,0.2), transparent 40%), url('../assets/thumb.svg') center/cover no-repeat; border: 1px solid var(--border); }

/* CTA Band */
.cta-band { border-top: 1px solid var(--border); border-bottom: 1px solid var(--border); background: linear-gradient(180deg, rgba(122,92,255,0.08), rgba(0,225,255,0.08)); }
.cta-band .container { padding: 2rem 0; text-align: center; }

/* Footer */
.site-footer { border-top: 1px solid var(--border); background: rgba(11,15,20,0.8); margin-top: 2rem; }
.footer-grid { display: grid; grid-template-columns: 2fr 1fr 1fr; gap: 1rem; padding: 1.5rem 0; }
.list { list-style: none; margin: 0; padding: 0; display: grid; gap: .4rem; }
.brand.small { font-size: .95rem; }

/* Cart Drawer */
.cart-drawer { position: fixed; inset: 0 0 0 auto; width: min(420px, 95vw); transform: translateX(110%); transition: transform .25s ease; background: var(--bg-elev); border-left: 1px solid var(--border); box-shadow: -20px 0 50px rgba(0,0,0,.3); z-index: 60; }
.cart-drawer.open { transform: translateX(0); }
.cart-content { position: absolute; inset: 0; display: grid; grid-template-rows: auto 1fr auto; padding: 1rem; gap: 1rem; }
.cart-items { overflow: auto; display: grid; gap: .8rem; }
.cart-footer { display: flex; align-items: center; justify-content: space-between; gap: 1rem; padding-top: .6rem; border-top: 1px solid var(--border); }
.cart-close { position: absolute; top: .6rem; right: .6rem; background: transparent; color: var(--text); border: 1px solid var(--border); border-radius: .4rem; width: 32px; height: 32px; }

/* Micro-interactions */
.tilt { transform-style: preserve-3d; perspective: 600px; }
.reveal { opacity: 0; translate: 0 10px; transition: opacity .5s ease, translate .5s ease; }
.reveal.visible { opacity: 1; translate: 0 0; }

/* Responsive */
@media (max-width: 900px) {
  .grid.four { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid.three { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .footer-grid { grid-template-columns: 1fr 1fr; }
}
@media (max-width: 640px) {
  .menu { display: none; position: absolute; top: 100%; right: 1rem; background: var(--bg-elev); border: 1px solid var(--border); border-radius: .6rem; padding: .5rem; flex-direction: column; width: 240px; }
  .menu.open { display: flex; }
  .nav-toggle { display: inline-flex; background: transparent; color: var(--text); border: 1px solid var(--border); border-radius: .4rem; padding: .4rem .6rem; }
  .hero-stats { grid-template-columns: 1fr; }
}

