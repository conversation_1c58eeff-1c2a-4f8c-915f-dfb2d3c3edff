// Shop page functionality: quantity selectors, filtering, sorting, and cart notifications
(function () {
  const $ = (s, el = document) => el.querySelector(s);
  const $$ = (s, el = document) => Array.from(el.querySelectorAll(s));

  // Quantity selector functionality
  $$('.quantity-selector').forEach(selector => {
    const decreaseBtn = $('.qty-btn[data-action="decrease"]', selector);
    const increaseBtn = $('.qty-btn[data-action="increase"]', selector);
    const input = $('.qty-input', selector);

    decreaseBtn?.addEventListener('click', () => {
      const current = parseInt(input.value) || 1;
      const min = parseInt(input.getAttribute('min')) || 1;
      if (current > min) {
        input.value = current - 1;
        updateAddToCartButton(selector.closest('.product-card'));
      }
    });

    increaseBtn?.addEventListener('click', () => {
      const current = parseInt(input.value) || 1;
      const max = parseInt(input.getAttribute('max')) || 99;
      if (current < max) {
        input.value = current + 1;
        updateAddToCartButton(selector.closest('.product-card'));
      }
    });

    input?.addEventListener('change', () => {
      const min = parseInt(input.getAttribute('min')) || 1;
      const max = parseInt(input.getAttribute('max')) || 99;
      let value = parseInt(input.value) || min;
      
      if (value < min) value = min;
      if (value > max) value = max;
      
      input.value = value;
      updateAddToCartButton(selector.closest('.product-card'));
    });
  });

  // Update add to cart button with quantity
  function updateAddToCartButton(card) {
    const qtyInput = $('.qty-input', card);
    const addBtn = $('.add-to-cart', card);
    const quantity = parseInt(qtyInput.value) || 1;
    
    // Update data-qty attribute for cart functionality
    addBtn.setAttribute('data-qty', quantity);
  }

  // Enhanced add to cart with visual feedback
  document.addEventListener('click', (e) => {
    const addBtn = e.target.closest('.add-to-cart');
    if (!addBtn) return;
    
    e.preventDefault();
    
    // Get product details
    const card = addBtn.closest('.product-card');
    const qtyInput = $('.qty-input', card);
    const quantity = parseInt(qtyInput.value) || 1;
    const title = addBtn.getAttribute('data-title');
    
    // Add loading state
    card.classList.add('loading');
    addBtn.textContent = 'Adding...';
    
    // Simulate add to cart delay
    setTimeout(() => {
      card.classList.remove('loading');
      addBtn.textContent = 'Add to Cart';
      
      // Show notification
      showCartNotification(`Added ${quantity}x ${title} to cart!`);
      
      // Trigger cart update (handled by existing shopify.js)
      const variant = addBtn.getAttribute('data-variant');
      const price = parseFloat(addBtn.getAttribute('data-price') || '0');
      
      if (window.Cart && variant) {
        window.Cart.add(variant, title, price, quantity);
      }
    }, 500);
  });

  // Cart notification system
  function showCartNotification(message) {
    // Remove existing notification
    const existing = $('.cart-notification');
    if (existing) existing.remove();
    
    // Create new notification
    const notification = document.createElement('div');
    notification.className = 'cart-notification';
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);
    
    // Hide and remove notification
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  // Product filtering and sorting
  const sortFilter = $('#sort-filter');
  const categoryFilter = $('#category-filter');
  const productsGrid = $('#products-grid');
  const productCards = $$('.product-card');

  function filterAndSort() {
    const sortValue = sortFilter?.value || 'name';
    const categoryValue = categoryFilter?.value || 'all';
    
    // Filter by category
    let visibleCards = productCards.filter(card => {
      const category = card.getAttribute('data-category');
      const shouldShow = categoryValue === 'all' || category === categoryValue;
      
      if (shouldShow) {
        card.classList.remove('filtered-out');
      } else {
        card.classList.add('filtered-out');
      }
      
      return shouldShow;
    });
    
    // Sort visible cards
    visibleCards.sort((a, b) => {
      switch (sortValue) {
        case 'price-low':
          return parseFloat(a.getAttribute('data-price')) - parseFloat(b.getAttribute('data-price'));
        case 'price-high':
          return parseFloat(b.getAttribute('data-price')) - parseFloat(a.getAttribute('data-price'));
        case 'category':
          return a.getAttribute('data-category').localeCompare(b.getAttribute('data-category'));
        case 'name':
        default:
          const nameA = $('h3', a)?.textContent || '';
          const nameB = $('h3', b)?.textContent || '';
          return nameA.localeCompare(nameB);
      }
    });
    
    // Reorder DOM elements
    visibleCards.forEach(card => {
      productsGrid.appendChild(card);
    });
  }

  // Bind filter events
  sortFilter?.addEventListener('change', filterAndSort);
  categoryFilter?.addEventListener('change', filterAndSort);

  // Initialize quantities on page load
  $$('.product-card').forEach(updateAddToCartButton);

  // Expose Cart for external access (from shopify.js)
  if (typeof window.Cart !== 'undefined') {
    // Cart is already available from shopify.js
  } else {
    console.warn('Cart functionality not available. Make sure shopify.js is loaded.');
  }

  // Search functionality (bonus feature)
  function addSearchBox() {
    const searchHTML = `
      <div class="search-box" style="margin-bottom: 1rem;">
        <input type="text" id="product-search" placeholder="Search products..." 
               style="width: 100%; padding: 0.75rem; border: 1px solid #e9ecef; border-radius: 0.5rem; font-size: 1rem;">
      </div>
    `;
    
    const shopHeader = $('.shop-header');
    if (shopHeader) {
      shopHeader.insertAdjacentHTML('afterend', searchHTML);
      
      const searchInput = $('#product-search');
      searchInput?.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        
        productCards.forEach(card => {
          const title = $('h3', card)?.textContent.toLowerCase() || '';
          const description = $('.product-description', card)?.textContent.toLowerCase() || '';
          const matches = title.includes(searchTerm) || description.includes(searchTerm);
          
          if (matches || searchTerm === '') {
            card.style.display = '';
          } else {
            card.style.display = 'none';
          }
        });
      });
    }
  }

  // Initialize search (optional)
  // addSearchBox();

})();
