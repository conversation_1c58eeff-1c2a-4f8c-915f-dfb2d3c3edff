<!doctype html>
<html lang="en" data-theme="dark">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Choosing Materials – Blog | LayerLab</title>
  <link rel="stylesheet" href="/css/styles.css" />
</head>
<body>
  <header class="site-header">
    <div class="container nav">
      <a href="/" class="brand">
        <svg class="logo" width="32" height="32" viewBox="0 0 32 32" aria-hidden="true">
          <defs>
            <linearGradient id="logoGrad7" x1="0" x2="1" y1="0" y2="1">
              <stop offset="0" stop-color="#00e1ff"/>
              <stop offset="1" stop-color="#7a5cff"/>
            </linearGradient>
          </defs>
          <rect x="4" y="24" width="24" height="4" rx="1" fill="url(#logoGrad7)" opacity="0.9"/>
          <rect x="6" y="20" width="20" height="4" rx="1" fill="url(#logoGrad7)" opacity="0.7"/>
          <rect x="8" y="16" width="16" height="4" rx="1" fill="url(#logoGrad7)" opacity="0.5"/>
          <rect x="10" y="12" width="12" height="4" rx="1" fill="url(#logoGrad7)" opacity="0.3"/>
          <rect x="12" y="8" width="8" height="4" rx="1" fill="url(#logoGrad7)" opacity="0.2"/>
          <rect x="14" y="4" width="4" height="4" rx="1" fill="url(#logoGrad7)" opacity="0.1"/>
        </svg>
        <span class="brand-text">LayerLab</span>
      </a>
      <nav aria-label="Primary">
        <button class="nav-toggle" aria-expanded="false" aria-controls="menu">☰</button>
        <ul id="menu" class="menu">
          <li><a href="/services.html">Services</a></li>
          <li><a href="/portfolio.html">Portfolio</a></li>
          <li><a href="/about.html">About</a></li>
          <li><a href="/blog/" aria-current="page">Blog</a></li>
          <li><a href="/contact.html" class="btn btn-primary">Get a Quote</a></li>
        </ul>
      </nav>
      <a href="#" class="cart-link" aria-label="Cart" data-cart-toggle>
        <span class="icon-cart" aria-hidden="true"></span>
        <span class="cart-count" aria-live="polite">0</span>
      </a>
    </div>
  </header>

  <main id="main" class="container">
    <article class="card">
      <h1>Choosing materials: PLA vs PETG vs ABS</h1>
      <p class="muted">Strength, heat resistance, and finish tradeoffs explained.</p>
      <p>Content goes here…</p>
    </article>
  </main>

  <footer class="site-footer">
    <div class="container footer-grid">
      <div>
        <div class="brand small">
          <svg class="logo small" width="20" height="20" viewBox="0 0 32 32" aria-hidden="true">
            <defs>
              <linearGradient id="logoGradSmall7" x1="0" x2="1" y1="0" y2="1">
                <stop offset="0" stop-color="#00e1ff"/>
                <stop offset="1" stop-color="#7a5cff"/>
              </linearGradient>
            </defs>
            <rect x="4" y="24" width="24" height="4" rx="1" fill="url(#logoGradSmall7)" opacity="0.9"/>
            <rect x="6" y="20" width="20" height="4" rx="1" fill="url(#logoGradSmall7)" opacity="0.7"/>
            <rect x="8" y="16" width="16" height="4" rx="1" fill="url(#logoGradSmall7)" opacity="0.5"/>
            <rect x="10" y="12" width="12" height="4" rx="1" fill="url(#logoGradSmall7)" opacity="0.3"/>
            <rect x="12" y="8" width="8" height="4" rx="1" fill="url(#logoGradSmall7)" opacity="0.2"/>
            <rect x="14" y="4" width="4" height="4" rx="1" fill="url(#logoGradSmall7)" opacity="0.1"/>
          </svg>
          LayerLab
        </div>
        <p class="muted">High‑quality custom 3D printing for makers and engineers.</p>
      </div>
      <nav aria-label="Footer">
        <ul class="list">
          <li><a href="/services.html">Services</a></li>
          <li><a href="/portfolio.html">Portfolio</a></li>
          <li><a href="/about.html">About</a></li>
          <li><a href="/blog/">Blog</a></li>
          <li><a href="/contact.html">Contact</a></li>
        </ul>
      </nav>
      <div>
        <p class="muted">© <span id="year"></span> LayerLab</p>
      </div>
    </div>
  </footer>

  <script src="/js/main.js" defer></script>
  <script src="/js/shopify.js" defer></script>
</body>
</html>

